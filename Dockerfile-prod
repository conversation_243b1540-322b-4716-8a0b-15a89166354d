# 第一阶段：构建阶段（保持原有node 14.17.1版本）
FROM registry.cn-hangzhou.aliyuncs.com/leo_library/node:14.17.1 as build
WORKDIR /tmp
# 1. 先只复制package文件，利用Docker缓存
COPY package.json ./
# 2. 安装依赖（限制内存使用）
RUN npm config set strict-ssl false && \
    npm install --legacy-peer-deps --production --no-optional && \
    npm cache clean --force
# 3. 复制剩余文件
COPY . .
# 4. 构建时严格限制内存
ENV NODE_OPTIONS="--max-old-space-size=256"
RUN npm run build:prod
# 第二阶段：运行阶段（保持原有nginx 1.12.2版本）
FROM registry.cn-hangzhou.aliyuncs.com/leo_library/nginx:1.12.2
WORKDIR /usr/share/nginx/html/charging-maintenance-ui
RUN rm -f *
COPY --from=build /tmp/dist .
#本地工程中修改nginx配置-废弃
COPY --from=build /tmp/nginx.conf /etc/nginx/nginx.conf
